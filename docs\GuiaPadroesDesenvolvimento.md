<div align="center">
  <a href="https://www.inteli.edu.br/">
    <img src="./assets/logo_inteli_btg.png" 
         alt="Imagem contendo a logotipo do Banco BTG Pactual e do Inteli" 
         border="0" 
         style="max-width: 600px; width: 100%; height: auto;">
  </a>
</div>

<br/>

# Guia de Padrões de Desenvolvimento - PATI
## Plataforma de Adequação de Tipo de Investidor

### Nome do Grupo: MMs (Meninas Malvadas)

---

## Sumário
- [Introdução](#introdução)
- [Estrutura de Codificação no Gitflow](#estrutura-de-codificação-no-gitflow)
- [Padrões de Codificação e Padrões de Projeto](#padrões-de-codificação-e-padrões-de-projeto)
- [Desenvolvimento](#desenvolvimento)
- [Execução e Documentação de Testes](#execução-e-documentação-de-testes)
- [Políticas de Gestão de Configuração](#políticas-de-gestão-de-configuração)

---

## Introdução

Este guia estabelece os padrões de desenvolvimento para o projeto PATI, garantindo consistência, qualidade e manutenibilidade do código. Todos os membros da equipe devem seguir estas diretrizes para manter a coesão do projeto.

---

## Estrutura de Codificação no Gitflow

### Conventional Commits

**Obrigatório**: Use prefixos padronizados em todas as mensagens de commit.

#### Prefixos Aceitos:
- `feat`: Nova funcionalidade
- `fix`: Correção de bug
- `docs`: Mudanças na documentação
- `style`: Formatação (sem mudança de lógica)
- `refactor`: Refatoração de código
- `test`: Adição ou correção de testes
- `chore`: Tarefas de manutenção

#### Formato:
```
<tipo>(<escopo>): <descrição>

<corpo opcional>

<rodapé opcional>
```

#### Exemplos Corretos:
```bash
feat(database): adiciona índice composto para tabela client
fix(api): corrige validação de perfil de risco
docs(readme): atualiza instruções de instalação
test(classification): adiciona testes unitários para SVD
refactor(services): reorganiza estrutura de diretórios
```

#### Exemplos Incorretos:
```bash
❌ "Correção de bug"
❌ "Atualização"
❌ "WIP"
❌ "Fix"
```

### Nomenclatura de Branches

**Padrão Obrigatório**: `<tipo>/<nome-descritivo>`

#### Tipos de Branch:
- `feature/` - Novas funcionalidades
- `bugfix/` - Correções de bugs
- `hotfix/` - Correções urgentes
- `docs/` - Atualizações de documentação
- `refactor/` - Refatorações

#### Exemplos:
```bash
feature/add-client-index
feature/implement-svd-algorithm
bugfix/fix-authentication-error
docs/update-installation-manual
refactor/reorganize-services-structure
```

### Pull Requests Detalhados

**Obrigatório**: Todos os PRs devem seguir o template abaixo.

#### Template de PR:
```markdown
## O que foi alterado
- Descrição clara e objetiva das mudanças

## Por que foi alterado
- Justificativa técnica ou de negócio
- Referência a issues (#123)

## Como testar
- Passos específicos para validação
- Comandos ou URLs para teste

## Screenshots (se aplicável)
- Evidências visuais das mudanças

## Checklist
- [ ] Testes unitários adicionados/atualizados
- [ ] Documentação atualizada
- [ ] Code review realizado
- [ ] Sem breaking changes
- [ ] Conventional commits utilizados
```

#### Exemplo Prático:
```markdown
## O que foi alterado
- Adicionado índice composto `idx_client_profile` na tabela `client`
- Otimizada consulta de clientes por perfil de risco

## Por que foi alterado
- Melhorar performance das consultas frequentes por perfil e nível de risco
- Reduzir tempo de resposta da API de classificação de 5s para <2s

## Como testar
1. Executar consulta: `SELECT * FROM client WHERE risk_profile_form = 'Moderado'`
2. Verificar plano de execução com `EXPLAIN ANALYZE`
3. Testar endpoint: `GET /api/clients?profile=Moderado`
4. Verificar tempo de resposta < 2s

## Screenshots
![Performance antes/depois](link-para-imagem)

## Checklist
- [x] Testes unitários adicionados
- [x] Documentação do banco atualizada
- [x] Code review realizado
- [x] Sem breaking changes
- [x] Conventional commits utilizados
```

---

## Padrões de Codificação e Padrões de Projeto

### Estrutura de Diretórios

**Obrigatório**: Seguir Clean Architecture para todos os serviços.

#### Backend (.NET):
```
src/services/nome-do-servico/
├── src/
│   ├── Controllers/          # Camada de apresentação
│   ├── Services/             # Camada de aplicação
│   ├── Interfaces/           # Contratos e abstrações
│   ├── Models/               # Entidades e DTOs
│   ├── Repositories/         # Camada de dados
│   └── Infrastructure/       # Configurações
├── tests/                    # Testes unitários e integração
├── Dockerfile
└── appsettings.json
```

#### Frontend (React Native):
```
src/frontend/pati/
├── src/
│   ├── components/          # Componentes reutilizáveis
│   ├── screens/             # Telas da aplicação
│   ├── services/            # Chamadas para APIs
│   ├── hooks/               # Custom hooks
│   ├── utils/               # Utilitários
│   ├── types/               # Tipos TypeScript
│   └── navigation/          # Configuração de navegação
├── __tests__/               # Testes
└── assets/                  # Recursos
```

### Interfaces Obrigatórias

**Todos os serviços** devem implementar interfaces:

```csharp
// Exemplo: IClassificationService
public interface IClassificationService
{
    Task<ClassificationResult> ClassifyPortfolioAsync(int clientId);
    Task<IEnumerable<Recommendation>> GetRecommendationsAsync(int clientId);
}

public class ClassificationService : IClassificationService
{
    private readonly IClientRepository _clientRepository;
    private readonly IMLModelService _mlModelService;
    
    public ClassificationService(
        IClientRepository clientRepository,
        IMLModelService mlModelService)
    {
        _clientRepository = clientRepository;
        _mlModelService = mlModelService;
    }
    
    // Implementação...
}
```

### Repository Pattern

**Obrigatório** para acesso a dados:

```csharp
public interface IClientRepository
{
    Task<Client> GetByIdAsync(int id);
    Task<IEnumerable<Client>> GetNonCompliantAsync();
    Task AddAsync(Client client);
    Task UpdateAsync(Client client);
}
```

### Injeção de Dependência

**Configuração obrigatória** no `Program.cs`:

```csharp
// Program.cs
builder.Services.AddScoped<IClassificationService, ClassificationService>();
builder.Services.AddScoped<IClientRepository, ClientRepository>();
builder.Services.AddScoped<IMLModelService, MLModelService>();
```

---

## Desenvolvimento

### Tratamento de Erros

**Obrigatório**: Implementar middleware global de tratamento de erros.

```csharp
public class GlobalExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionMiddleware> _logger;

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro não tratado: {Message}", ex.Message);
            await HandleExceptionAsync(context, ex);
        }
    }
}
```

### Logging Estruturado

**Obrigatório**: Usar Serilog com informações estruturadas.

```csharp
_logger.LogInformation(
    "Classificação concluída para cliente {ClientId} em {ElapsedMs}ms. Resultado: {Result}",
    clientId, stopwatch.ElapsedMilliseconds, result.RiskProfile);
```

### Configurações

**Obrigatório**: Usar `appsettings.json` e padrão Options.

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=pati_db;Username=pati_user;Password=pati_password_2025"
  },
  "MLModel": {
    "ModelPath": "models/modelo_svd_treinado.pkl",
    "ConfidenceThreshold": 0.7
  }
}
```

### Validação de Entrada

**Obrigatório**: Usar FluentValidation em todos os endpoints.

```csharp
public class ClassificationRequestValidator : AbstractValidator<ClassificationRequest>
{
    public ClassificationRequestValidator()
    {
        RuleFor(x => x.ClientId)
            .GreaterThan(0)
            .WithMessage("ID do cliente deve ser maior que zero");
            
        RuleFor(x => x.Portfolio)
            .NotEmpty()
            .WithMessage("Portfolio não pode estar vazio");
    }
}
```

---

## Execução e Documentação de Testes

### Estratégia de Testes

#### Cobertura Obrigatória:
- **Testes Unitários**: 85% de cobertura mínima
- **Testes de Integração**: Todos os endpoints
- **Testes End-to-End**: Fluxos críticos

#### Ferramentas:
- **.NET**: xUnit + Moq + TestContainers
- **React Native**: Jest + React Native Testing Library
- **APIs**: Postman/Newman + K6

### Documentação de Testes

**Obrigatório**: Documentar estratégia e execução.

#### Estrutura da Documentação:
```markdown
# Documentação de Testes

## Estratégia de Testes
- **Unitários**: Cobrem serviços, controllers e repositories
- **Integração**: Testam fluxos completos com test containers
- **E2E**: Validam jornadas do usuário

## Como Executar
```bash
# Testes unitários
dotnet test

# Testes de integração
docker-compose -f docker-compose.test.yml up
dotnet test --filter Category=Integration

# Testes de performance
k6 run performance-tests.js
```

## Relatórios
- Cobertura: `coverage/index.html`
- Resultados: `test-results.xml`
```

### Exemplo de Teste Unitário

```csharp
[Fact]
public async Task ClassifyAsync_ValidClient_ReturnsClassificationResult()
{
    // Arrange
    var clientId = 1;
    var client = new Client { Id = clientId, Portfolio = new Portfolio() };
    var expectedResult = new ClassificationResult { RiskProfile = "Moderado" };

    _clientRepositoryMock
        .Setup(x => x.GetByIdAsync(clientId))
        .ReturnsAsync(client);

    // Act
    var result = await _service.ClassifyAsync(clientId);

    // Assert
    Assert.Equal(expectedResult.RiskProfile, result.RiskProfile);
}
```

---

## Políticas de Gestão de Configuração

### Conventional Commits (Repetição para Ênfase)

**Aplicar as correções de Gitflow** conforme especificado na seção anterior.

### Code Review Obrigatório

#### Critérios para Aprovação:
- [ ] Conventional commits utilizados
- [ ] Testes adicionados/atualizados
- [ ] Documentação atualizada
- [ ] Sem código duplicado
- [ ] Interfaces implementadas
- [ ] Tratamento de erros adequado
- [ ] Logging estruturado
- [ ] Validação de entrada

#### Processo:
1. **Criar PR** seguindo o template
2. **Solicitar review** de pelo menos 2 membros
3. **Corrigir** feedback recebido
4. **Aguardar aprovação** de todos os reviewers
5. **Merge** apenas após aprovação

### Branches e Merge

#### Regras:
- **Nunca** fazer commit direto na `main`
- **Sempre** usar feature branches
- **Obrigatório** squash commits no merge
- **Deletar** branch após merge

#### Fluxo:
```bash
# 1. Criar branch
git checkout -b feature/nova-funcionalidade

# 2. Desenvolver e commitar
git add .
git commit -m "feat(api): adiciona endpoint de classificação"

# 3. Push e criar PR
git push origin feature/nova-funcionalidade

# 4. Após aprovação, merge com squash
# 5. Deletar branch
git branch -d feature/nova-funcionalidade
```

---

## Conclusão

Este guia deve ser seguido rigorosamente por todos os membros da equipe. Qualquer dúvida ou sugestão de melhoria deve ser discutida em reunião de equipe.

**Lembre-se**: A qualidade do código é responsabilidade de todos!

---

**Documento elaborado por**: Equipe MMs (Meninas Malvadas)  
**Data**: Junho 2025  
**Versão**: 1.0
